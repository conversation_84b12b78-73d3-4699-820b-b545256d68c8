import { Image, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomModal from '../CustomModal'
import { MaterialCommunityIcons } from '@expo/vector-icons'
import useBluetoothStore from '../../../store/BluetoothStore'
import { useTheme } from '../../../context/ThemeContext'
import SimpleBtn from '../../Buttons/SimpleBtn'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import SelectBox, { SelectOption } from '../../FormFields/SelectBox'
import SelectBoxWithLabel from '../../FormFields/SelectBoxWithText'

const TabletDispenseingGif = require('../../../../assets/tablet_dispensing.gif')

type TabletScheduleModalProps = {}

const TabletScheduleModal = ({}:TabletScheduleModalProps) => {

    const { theme } = useTheme();

    const showScheduleTabletModalId = useBluetoothStore(state => state.showScheduleTabletModalId);
    const setScheduleTabletModal = useBluetoothStore(state => state.setScheduleTabletModal);

    const [selectedHour, setSelectedHour] = useState<string>('7:30');
    const [selectedPeriod, setSelectedPeriod] = useState<string>('AM');
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState<string | null>(null);

    const [ isScheduling, setIsScheduling ] = useState(false);
    const [ isSchedulingDone,setIsSchedulingDone ] = useState(false);

    const visible = showScheduleTabletModalId !== null;

    // Time options for the dropdown
    const timeOptions: SelectOption[] = [
      { label: '1:00', value: '1:00' },
      { label: '1:30', value: '1:30' },
      { label: '2:00', value: '2:00' },
      { label: '2:30', value: '2:30' },
      { label: '3:00', value: '3:00' },
      { label: '3:30', value: '3:30' },
      { label: '4:00', value: '4:00' },
      { label: '4:30', value: '4:30' },
      { label: '5:00', value: '5:00' },
      { label: '5:30', value: '5:30' },
      { label: '6:00', value: '6:00' },
      { label: '6:30', value: '6:30' },
      { label: '7:00', value: '7:00' },
      { label: '7:30', value: '7:30' },
      { label: '8:00', value: '8:00' },
      { label: '8:30', value: '8:30' },
      { label: '9:00', value: '9:00' },
      { label: '9:30', value: '9:30' },
      { label: '10:00', value: '10:00' },
      { label: '10:30', value: '10:30' },
      { label: '11:00', value: '11:00' },
      { label: '11:30', value: '11:30' },
      { label: '12:00', value: '12:00' },
    ];

    // AM/PM options
    const periodOptions: SelectOption[] = [
        { label: 'AM', value: 'AM' },
        { label: 'PM', value: 'PM' },
    ];

    const handleSchedule = () => {
        setIsScheduling(true);
        setTimeout(() => {
            setIsScheduling(false);
            setIsSchedulingDone(true);
        }, 2000);
    };

    const handleClose = () => {
        if(isScheduling) return;
        setScheduleTabletModal(null);
    };

    useEffect(() => {
        if(!visible) {
            setIsScheduling(false);
            setIsSchedulingDone(false);
        }
    }, [showScheduleTabletModalId]);

  return (
    <CustomModal
      visible={visible}
      onClose={handleClose}
      title="Schedule"
      subtitle={isSchedulingDone ? "Successful" : "Dispense"}
      icon={
        <View style={styles.scheduleIconContainer}>
            {
                isSchedulingDone && (
                    <View style={styles.tabletScheduleContainer}>
                        <Text style={[styles.scheduledTimeText, { color: theme.text.senary }]}>7:30pm</Text>
                        <MaterialCommunityIcons name="checkbox-marked-circle" size={20} color={theme.toast.success} />
                    </View>
                )
            }
            <Image source={TabletDispenseingGif} style={styles.gifImage} />
            {
                !isSchedulingDone && (
                    <View style={styles.timeSelectionContainer}>
                        <SelectBoxWithLabel
                            label='Choose Time'
                            placeholder="7:30"
                            options={timeOptions}
                            selectedValue={selectedHour}
                            onValueChange={(value) => setSelectedHour(value as string)}
                            dropdownId="hour-selector"
                            currentOpenDropdown={currentOpenDropdown}
                            setCurrentOpenDropdown={setCurrentOpenDropdown}
                            listZ={999}
                            triggerZ={998}
                        />

                        <SelectBox
                            placeholder="AM"
                            options={periodOptions}
                            selectedValue={selectedPeriod}
                            onValueChange={(value) => setSelectedPeriod(value as string)}
                            dropdownId="period-selector"
                            currentOpenDropdown={currentOpenDropdown}
                            setCurrentOpenDropdown={setCurrentOpenDropdown}
                            listZ={997}
                            triggerZ={996}
                        />
                    </View>
                )
            }
        </View>
      }
      buttons={
            <View style={styles.timeSelectionContainer}>
                <SimpleBtn
                    title={isSchedulingDone?"Okay":(isScheduling ? "Scheduling..." : "Schedule")}
                    onPress={isSchedulingDone?handleClose:handleSchedule}
                    containerStyle={
                        isSchedulingDone?styles.scheduleDoneButton:styles.scheduleButton
                    }
                    containerBgColor="primary"
                    titleTextColor="secondary"
                    disabled={isScheduling}
                    disabledBgColor='primary'
                    disabledTitleColor='secondary'
                />
            </View>
      }
      showBackBtn={!isSchedulingDone}
    />
  )
}

export default TabletScheduleModal

const styles = StyleSheet.create({
  scheduleIconContainer: {
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginBottom: 8
  },
  gifImage: {
    width: 160,
    height: 160,
    resizeMode: "contain",
    alignSelf: "center"
  },
  tabletScheduleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  scheduledTimeText: {
    fontSize: 26,
    fontFamily: AppFonts.HelixaBold,
    textAlign: "center",
    marginRight: 2
  },
  timeSelectionContainer: {
    width:"100%",
    gap: 8,
  },
  scheduleButton: {
    width: "auto",
    alignSelf: "center",
    paddingHorizontal: 24,
    marginTop: 12
  },
  scheduleDoneButton: {
    width: "auto",
    alignSelf: "center",
    paddingHorizontal: 48,
  }
})