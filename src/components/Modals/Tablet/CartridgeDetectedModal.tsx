import { Image, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomModal from '../CustomModal'
import { MaterialCommunityIcons } from '@expo/vector-icons'
import useBluetoothStore from '../../../store/BluetoothStore'
import { useTheme } from '../../../context/ThemeContext'
import SimpleBtn from '../../Buttons/SimpleBtn'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import SelectBox, { SelectOption } from '../../FormFields/SelectBox'
import SelectBoxWithLabel from '../../FormFields/SelectBoxWithText'
import CartridgeUI from '../../Icons/Cartridge/CartridgeUI'

const TabletDispenseingGif = require('../../../../assets/tablet_dispensing.gif')

type CartridgeDetectedModalProps = {}

const CartridgeDetectedModal = ({}:CartridgeDetectedModalProps) => {

    const { theme } = useTheme();
    const [ visible, setVisible ] = useState(true);

    const handleClose = () => {
        setVisible(false);
    };

  return (
    <CustomModal
      visible={visible}
      onClose={handleClose}
      title="Cartridge Status"
      subtitle={"Cartridge Detected"}
      icon={
        <View style={styles.scheduleIconContainer}>
            <CartridgeUI cartidgeColor="#417739" tabletColor="#9CC5B0" numTablets={3} containerStyle={styles.cartridgeContainer} tabletContainerStyle={styles.tabletContainer}/>
        </View>
      }
      buttons={
            <View style={styles.timeSelectionContainer}>
                <SimpleBtn
                    title={"Okay"}
                    onPress={handleClose}
                    containerStyle={
                        styles.okayBtn
                    }
                    containerBgColor="primary"
                    titleTextColor="secondary"
                    disabledBgColor='primary'
                    disabledTitleColor='secondary'
                />
            </View>
      }
    />
  )
}

export default CartridgeDetectedModal

const styles = StyleSheet.create({
  scheduleIconContainer: {
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginBottom: 8
  },
  cartridgeContainer: {
    height:150,
    width:41,
  },
  tabletContainer: {
    gap:1
  },
  scheduledTimeText: {
    fontSize: 26,
    fontFamily: AppFonts.HelixaBold,
    textAlign: "center",
    marginRight: 2
  },
  timeSelectionContainer: {
    width:"100%",
    gap: 8,
  },
  okayBtn: {
    width: "auto",
    alignSelf: "center",
    paddingHorizontal: 40,
  },
})