import { Image, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomModal from '../CustomModal'
import { Entypo, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons'
import useBluetoothStore from '../../../store/BluetoothStore'
import { useTheme } from '../../../context/ThemeContext'
import SimpleBtn from '../../Buttons/SimpleBtn'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import BottleWithWater from '../../Icons/Bottle/BottleWithWater'

const TabletDispenseingGif = require('../../../../assets/tablet_dispensing.gif')

type TabletDispenseModalProps = {}

const TabletDispenseModal = ({}:TabletDispenseModalProps) => {

    const { theme } = useTheme();

    const isConnected = useBluetoothStore(state => state.isConnected);
    const dispensingTabletId = useBluetoothStore(state => state.dispensingTabletId);
    const isDispensingTablet = useBluetoothStore(state => state.isDispensingTablet);
    const isDispensingInProgress = useBluetoothStore(state => state.isDispensingInProgress);
    const isDispensingSuccess = useBluetoothStore(state => state.isDispensingSuccess);
    const isDispensingErrorFromStore = useBluetoothStore(state => state.isDispensingError)
    const dispensingProgress = useBluetoothStore(state => state.dispensingProgress);
    const isSyncingDispenseWithServer = useBluetoothStore(state => state.isSyncingDispenseWithServer);

    const dispenseNutrition = useBluetoothStore(state => state.dispenseNutrition);
    const setDispensingTabletId = useBluetoothStore(state => state.setDispensingTabletId);
    const resetDispensingState = useBluetoothStore(state => state.resetDispensingState);
    const setDispensingProgress = useBluetoothStore(state => state.setDispensingProgress);

    const [visible, setVisible] = useState(false)
    const [timeoutError, setTimeoutError] = useState(false) 

    const isDispensingError = isDispensingErrorFromStore || timeoutError || !isConnected;

    useEffect(() => {
      if(isDispensingTablet){
        setVisible(true);
      }
    }, [isDispensingTablet]);

    useEffect(() => {
        if(!isDispensingInProgress) return;

        const interval = setInterval(() => {
          
          if(!isConnected){
            clearInterval(interval);
            return;
          }

          setDispensingProgress(prev => {
            if(prev >= 100){
              clearInterval(interval);
              return 100;
            }
            
            return prev<90?prev+4:prev;
          });
        }, 1000);

        return () => clearInterval(interval);
    }, [isDispensingInProgress]);

  useEffect(() => {
    if (!isDispensingInProgress) return

    const timeout = setTimeout(() => {
      setDispensingProgress(92)
      resetDispensingState();
      setTimeoutError(true)
    }, 90000)

    return () => clearTimeout(timeout)
  }, [isDispensingInProgress])

  const showWaterAlert = dispensingTabletId!=null && !visible && isConnected;
  
  if(showWaterAlert) return (
    <CustomModal
      visible={showWaterAlert}
      onClose={() => setDispensingTabletId(null)}
      title="Alert"
      subtitle=""
      icon={
        <View style={styles.statusWrapper}>
          <BottleWithWater liquidColor="#2EB1D9" />
        </View>
      }
      desc="Make sure there's enough water!"
      buttons={
        <SimpleBtn
          title="Dispense now"
          onPress={() => {
            dispenseNutrition();
          }}
          containerStyle={{
            width: 'auto',
            paddingHorizontal: 24,
            marginTop: 4,
            alignSelf:"center",
          }}
          containerBgColor="primary"
          titleTextColor="secondary"
          disabled={!isConnected || isDispensingTablet}
        />
      }
    />
  );

  const showDispenseModal = visible && !showWaterAlert;

  return (
    <CustomModal
      visible={showDispenseModal}
      onClose={()=>{}}
      title="Tablet Dispense"
      subtitle={!isDispensingSuccess ? "Bottle disconnected" : ""}
      icon={
        (isDispensingSuccess || isDispensingError) ? (
          <View style={styles.statusWrapper}>
            {
                isConnected ? (
                  <BottleWithWater liquidColor="#C88FB9" />
                ) : (
                  <View style={styles.disconnectBottleImgContainer}>
                      <Image source={require('../../../../assets/bottle-img-top.png')} style={styles.disconnectBottleImg} resizeMode='cover' />
                  </View>
                )
            }
            <View style={styles.statusContainer}>
              <Text style={[styles.statusText, { color: theme.text.primary }]}>
                {isDispensingError ? isConnected?"Tablet dispense failed":"Device disconnected" : "Tablet dispensed"}
              </Text>
              {isDispensingError ? (
                <Entypo name="circle-with-cross" size={20} color={theme.toast.error} />
              ) : (
                <MaterialCommunityIcons name="checkbox-marked-circle" size={20} color={theme.toast.success} />
              )}
            </View>
            <SimpleBtn
              title="Okay"
              onPress={() => {
                setVisible(false)
                setTimeoutError(false)
                // setDispensingTabletId(null);
                resetDispensingState();
              }}
              containerStyle={{
                width: 'auto',
                paddingHorizontal: 42,
                marginTop: 12
              }}
              containerBgColor="primary"
              titleTextColor="secondary"
            />
          </View>
        ) : (
          <View>
            <Image source={TabletDispenseingGif} style={styles.gifImage} />
            <Text style={[styles.completionPercentageText, { color: theme.text.quinary }]}>
              {`${dispensingProgress}%`}
            </Text>
            <View style={styles.progressContainer}>
              <View style={[styles.progress, { width: `${dispensingProgress}%` }]} />
            </View>
          </View>
        )
      }
      desc={
        (isDispensingSuccess || isDispensingError)
          ? ""
          : isSyncingDispenseWithServer ? "Syncing with server..." : `Dispensing in progress,\nPlease be patient...`
      }
      showBackBtn={false}
    />
  )
}

export default TabletDispenseModal

const styles = StyleSheet.create({
  gifImage: {
    width: 160,
    height: 160,
    resizeMode: "contain",
    alignSelf: "center"
  },
  completionPercentageText: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaBoldItalic,
    textAlign: "center",
    marginTop: 8
  },
  progressContainer: {
    width: "100%",
    height: 10,
    backgroundColor: "#b1b1b1ff",
    borderRadius: 10,
    overflow: "hidden",
    marginTop: 4,
    borderWidth: 1,
    borderColor: "#b1b1b1ff"
  },
  progress: {
    height: "100%",
    backgroundColor: "#FFFFFF"
  },
  statusWrapper: {
    alignItems: "center",
    justifyContent: "center",
    gap: 8
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4
  },
  statusText: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaBold,
    textAlign: "center"
  },
  bottleIconContainerWrapper: {
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    padding: 2
  },
  bottleIconContainer: {
    width: 56,
    aspectRatio: 9 / 14
  },
  disconnectBottleImgContainer: {
    width: '55%',
    height:"auto",
    aspectRatio: 1,
    overflow: "hidden",
    opacity:.6
  },
  disconnectBottleImg: {
    width: "100%",
    height: "100%",
    opacity:.6
  }
})