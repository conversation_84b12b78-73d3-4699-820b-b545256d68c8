import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import { useTheme } from '../../context/ThemeContext'

interface BulletPointTextProps {
    text:string
}

const BulletPointText = ({text}:BulletPointTextProps) => {
    const { theme } = useTheme();
    return (
        <View style={styles.container}>
            <View style={[styles.buttlePoint,{backgroundColor:theme.text.senary}]}></View>
            <Text style={styles.bulletPointText}>{text}</Text>
        </View>
    )
}

export default BulletPointText

const styles = StyleSheet.create({
    container:{
        flexDirection:"row",
        gap:8,
        alignItems:"center",
    },
    buttlePoint:{
        width:16,
        height:3,
        borderRadius:2,
        alignSelf:"flex-start",
        marginTop:8,
    },
    bulletPointText:{
        flex:1,
        fontSize:12,
        fontFamily:AppFonts.HelixaBold,
    }
})