import { Defs, LinearGradient, Pattern, Rect, Stop } from 'react-native-svg'

const FadePattern = () => {
  return (
    <Defs>
      {/* Define the gradient at top level */}
      <LinearGradient id="GradientFade" x1="0" y1="0" x2="0" y2="1">
        {/* Top 80% transparent */}
        <Stop offset="0" stopColor="white" stopOpacity={0} />
        <Stop offset="0.8" stopColor="white" stopOpacity={0} />
        {/* Bottom 20% solid white */}
        <Stop offset="1" stopColor="white" stopOpacity={.75} />
      </LinearGradient>

      {/* Pattern that uses the gradient */}
      <Pattern
        id="WhiteFadePattern"
        patternUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="100%"
        height="100%"
      >
        <Rect x="0" y="0" width="100%" height="100%" fill="url(#GradientFade)" />
      </Pattern>
    </Defs>
  );
};

export default FadePattern
