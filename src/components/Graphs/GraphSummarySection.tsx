import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import BulletPointText from './BulletPointText'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import { useTheme } from '../../context/ThemeContext'

interface GraphSummarySectionProps {
  percentage:number,
  highlights : string[]
}

const GraphSummarySection = ({percentage,highlights}:GraphSummarySectionProps) => {
  const { theme } = useTheme();
  return (
    <View style={styles.container}>
        <View style={styles.percentageContainer}>
            <Text style={[styles.percentageText,{color:theme.text.primary}]}>{percentage}%</Text>
            <Text style={[styles.percentageSubText,{color:theme.text.quinary}]}>(Average)</Text>
        </View>
        <View style={styles.bulletPointTextContainer}>
            {
              highlights?.map((highlight,index)=>(
                <BulletPointText key={index} text={highlight}/>
              ))
            }
        </View>
    </View>
  )
}

export default GraphSummarySection

const styles = StyleSheet.create({
  container:{
    flexDirection:"row",
    gap:8,
    alignItems:"flex-start",
    justifyContent:"space-between",
  },
  percentageContainer:{
    flexDirection:"column",
    alignItems:"center",
    justifyContent:"center",
  },
  percentageText:{
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
  },
  percentageSubText:{
    fontSize:10,
    fontFamily:AppFonts.HelixaBold,
  },
  bulletPointTextContainer:{
    flex:1,
  }
})