import React, { useEffect, useRef, useState, memo } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Animated,
  Easing,
  FlatList,
  LayoutChangeEvent,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ViewStyle,
  DimensionValue,
  Keyboard,
} from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";
import SkeletonItem from "../SkeletonLoader/AdvancedSkeletonLoader";
import { Theme } from "../../constants/theme/colors";
import { ThemedStyleProp } from "../../types/ThemeStyleType";
import { useTheme } from "../../context/ThemeContext";
import { AppFonts } from "../../constants/theme/fonts/fonts";

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface SelectBoxProps {
  placeholder?: string;
  placeholderTextColor?: keyof Theme["text"];
  options?: SelectOption[];
  selectedValue?: string | number;
  onValueChange?: (value: string | number) => void;
  disabled?: boolean;
  disabledBgColor?: keyof Theme["background"];
  backgroundColor?: keyof Theme["background"];
  triggerZ?: number;
  listZ?: number;
  currentOpenDropdown?: string | null;
  scrollToOffset?: (yPos: number) => void;
  changeBG?: boolean;
  width?: DimensionValue;
  textColor?: keyof Theme["text"];
  triggerStyle?: ThemedStyleProp<ViewStyle, "backgroundColor">;
  dropdownId: string;
  setCurrentOpenDropdown: (id: string | null) => void;
  optionsBgColor?: keyof Theme["background"];
  optionsBorderWidth?: number;
  optionsBorderRadius?: number;
  optionsBorderColor?: keyof Theme["border"];
  optionsTextColor?: keyof Theme["text"];
  activeOptionBgColor?: keyof Theme["button"];
  activeOptionTextColor?: keyof Theme["text"];
  triggerBorderWidth?: number;
  error?: string;
  clearValidationError?: () => void;
  alignDropdown?: "auto" | "flex-start" | "flex-end" | "center";
  disabledTextColor?: keyof Theme["text"];
  remeasureYpos?: boolean;
  duration?: number;
  loading?: boolean;
  maxOptionsToShow?: number;
  containerStyle?: ThemedStyleProp<ViewStyle, "backgroundColor">;
  scrollbarThumbColor?: keyof Theme["background"];
  selectBoxStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const SelectBox: React.FC<SelectBoxProps> = memo(({
  placeholder,
  placeholderTextColor = "tertiary",
  options = [],
  selectedValue,
  onValueChange,
  disabled,
  disabledBgColor = 'secondary',
  backgroundColor = 'secondary',
  triggerZ,
  listZ,
  currentOpenDropdown,
  scrollToOffset,
  changeBG,
  width = "auto",
  textColor = 'primary',
  triggerStyle = {},
  dropdownId,
  setCurrentOpenDropdown,
  optionsBgColor = 'primary',
  optionsBorderWidth = 1.5,
  optionsBorderRadius = 8,
  optionsBorderColor = 'tertiary',
  optionsTextColor = 'tertiary',
  activeOptionBgColor = 'tertiary',
  activeOptionTextColor = 'secondary',
  triggerBorderWidth = 1.5,
  error,
  clearValidationError,
  alignDropdown = "auto",
  disabledTextColor = 'secondary',
  remeasureYpos,
  duration = 400,
  loading = false,
  maxOptionsToShow = 3,
  containerStyle = {},
  scrollbarThumbColor = 'secondary',
  selectBoxStyle={}
}) => {
  const ref = useRef<View>(null);
  const { theme } = useTheme();
  const [triggerHeight, setTriggerHeight] = useState(0);
  const [optionsHeight, setOptionsHeight] = useState(-10);
  const [yPos, setYPos] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const [thumbHeight, setThumbHeight] = useState(0);
  const [thumbTop, setThumbTop] = useState(0);

  const borderColorAnimated = useRef(new Animated.Value(0)).current;

  const borderColor = borderColorAnimated.interpolate({ inputRange: [0, 1], outputRange: [theme.background[backgroundColor], theme.border[optionsBorderColor]] });

  const animatedHeight = useRef(new Animated.Value(triggerHeight)).current;

  const initialSelectedValue = selectedValue ?? "";
  const selectedLabel =
    options.find((option) => option.value === initialSelectedValue)?.label || placeholder;

  // Measure Y pos
  useEffect(() => {
    ref.current?.measureInWindow((_x, y) => {
      setYPos(y);
    });
  }, [ref, remeasureYpos]);

  // Toggle dropdown
  const toggleDropdown = () => {
    Keyboard.dismiss();
    if (scrollToOffset && !isVisible) {
      scrollToOffset(yPos);
    }

    if (!disabled) {
      const shouldOpen = !isVisible;
      setIsVisible(shouldOpen);

      Animated.timing(borderColorAnimated, {
        toValue: shouldOpen ? 1 : 0,
        duration: duration,
        useNativeDriver: false,
      }).start();

      if (shouldOpen) setCurrentOpenDropdown(dropdownId);

      Animated.timing(animatedHeight, {
        toValue: shouldOpen
          ? Math.min(maxOptionsToShow, options.length) * optionsHeight +
          triggerHeight -
          optionsBorderWidth * 2 + 6
          : triggerHeight,
        duration,
        easing: Easing.ease,
        useNativeDriver: false,
      }).start();
    }
  };

  // Close dropdown if another is open
  useEffect(() => {
    if (currentOpenDropdown !== dropdownId && isVisible) {
      setIsVisible(false);

      Animated.timing(borderColorAnimated, {
        toValue: 0,
        duration: duration,
        easing: Easing.ease,
        useNativeDriver: false,
      }).start();

      Animated.timing(animatedHeight, {
        toValue: triggerHeight,
        duration,
        useNativeDriver: false,
      }).start();
    }
  }, [currentOpenDropdown]);

  // Handle selection
  const handleSelect = (item: SelectOption) => {
    if (onValueChange) onValueChange(item.value);
    setIsVisible(false);

    Animated.timing(borderColorAnimated, {
      toValue: 0,
      duration: duration,
      easing: Easing.ease,
      useNativeDriver: false,
    }).start();

    Animated.timing(animatedHeight, {
      toValue: triggerHeight,
      duration,
      useNativeDriver: false,
    }).start();
  };

  const onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { contentOffset, contentSize, layoutMeasurement } = e.nativeEvent;
    const visibleRatio = layoutMeasurement.height / contentSize.height;
    const newThumbHeight = layoutMeasurement.height * visibleRatio;
    const newThumbTop = contentOffset.y * visibleRatio + triggerHeight;
    setThumbHeight(newThumbHeight);
    setThumbTop(newThumbTop);
  };

  if (loading) {
    return (
      <View style={[styles.container, { alignSelf: alignDropdown }]} ref={ref}>
        <SkeletonItem borderRadius={8} height={39} isLoading={loading} />
      </View>
    );
  }

  return (
    <View>
      <View style={[styles.container, { alignSelf: alignDropdown }, containerStyle]} ref={ref}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={toggleDropdown}
          onLayout={(event: LayoutChangeEvent) =>
            setTriggerHeight(event.nativeEvent.layout.height)
          }
          style={[
            styles.selectBox,
            // isVisible && styles.selectBoxActive,
            // disabled && styles.disabledSelectBox,
            selectBoxStyle,
            {
              backgroundColor: !disabled && changeBG ? theme.background[backgroundColor] : theme.background[disabledBgColor],
            },
            { borderColor: !disabled && changeBG ? theme.background[backgroundColor] : theme.background[disabledBgColor] },
            triggerZ ? { zIndex: triggerZ } : {},
            triggerStyle,
            { borderWidth: triggerBorderWidth, width },
          ]}
          disabled={disabled}
        >
          <View style={styles.selectionContainer}>
            <Text
              style={[
                styles.selectedValue,
                { color: selectedLabel == placeholder ? theme.text[placeholderTextColor] : theme.text[textColor] },
                disabled && { color: theme.text[disabledTextColor] },
                { fontFamily: selectedLabel == placeholder ? AppFonts.HelixaBoldItalic : AppFonts.HelixaBold },
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {selectedLabel}
            </Text>
          </View>
          <Ionicons
            name={isVisible ? "chevron-up" : "chevron-down"}
            size={20}
            color={disabled ? theme.text[disabledTextColor] : textColor}
          />
        </TouchableOpacity>

        {/* 
      <Animated.View style={{
        position:"absolute",
        top:0,
        left:0,
        right:0,
        height:animatedHeight,
        borderRadius: 8,
        borderWidth: 2,
        backgroundColor:"red",
        zIndex:triggerZ?triggerZ-1:0
      }} pointerEvents="box-only" ></Animated.View> */}

        <Animated.View
          style={[
            styles.dropdown,
            {
              paddingTop: triggerHeight - optionsBorderWidth * 2,
              display: triggerHeight !== 0 ? "flex" : "none",
            },
            listZ ? { zIndex: listZ } : {},
            { height: animatedHeight },
            // selectedLabel === options[0]?.label
            //   ? { backgroundColor: activeOptionBgColor }
            //   : { backgroundColor: optionsBgColor },
            {
              borderWidth: optionsBorderWidth,
              borderRadius: optionsBorderRadius,
              borderColor: borderColor,
            },
          ]}
          pointerEvents='box-none'
        >
          {optionsHeight === -10 && (
            <View
              style={[{ opacity: 0, position: "absolute" }]}
              onLayout={(event) => {
                const { height } = event.nativeEvent.layout;
                setOptionsHeight(height);
              }}
            >
              <TouchableOpacity disabled activeOpacity={0.8} style={styles.option}>
                <Text style={styles.optionText}>{"item"}</Text>
              </TouchableOpacity>
            </View>
          )}
          {(maxOptionsToShow < options.length) && (
            <View style={[styles.scrollbarTrack, {
              zIndex: listZ ? listZ + 1 : 9999
            }]}>
              <View style={[styles.scrollbarThumb, { height: thumbHeight, top: thumbTop, backgroundColor: theme.background[scrollbarThumbColor] }]} />
            </View>
          )}
          <FlatList
            data={options}
            keyExtractor={(item) => item.value.toString()}
            scrollEnabled={maxOptionsToShow < options.length}
            showsVerticalScrollIndicator={false}
            onScroll={onScroll}
            renderItem={({ item, index }) => {
              const isSelected = item.value === initialSelectedValue;
              const isDisabled = item.disabled;
              return (
                <TouchableOpacity
                  activeOpacity={1}
                  style={[
                    styles.option,
                    {
                      backgroundColor: theme.background[optionsBgColor],
                    },
                    {
                      marginRight: maxOptionsToShow < options.length ? 8 : 2
                    },
                    isSelected && styles.selectedOption,
                    // isDisabled && styles.disabledOption,
                    {
                      backgroundColor: isSelected
                        ? theme.button[activeOptionBgColor]
                        : theme.background[optionsBgColor],
                    },
                    { marginBottom: index === options.length - 1 ? 6 : 0 },
                  ]}
                  onPress={() => !isDisabled && handleSelect(item)}
                  onPressIn={() => clearValidationError?.()}
                  disabled={isDisabled}
                >
                  <Text
                    style={[
                      styles.optionText,
                      { color: theme.text[optionsTextColor] },
                      isSelected && { color: theme.text[activeOptionTextColor] },
                      // isDisabled && styles.disabledOptionText,
                    ]}
                  >
                    {item.label}
                  </Text>
                </TouchableOpacity>
              );
            }}
          />
        </Animated.View>
      </View>
       {error&&(
          <Text 
            style={[styles.errorText,
              {color:theme.text["error"]}
            ]}
          >
            {error}
          </Text>
        )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: { position: "relative" },
  selectBox: {
    position: "relative",
    zIndex: 10,
    borderWidth: 0,
    borderRadius: 8,
    paddingVertical: 8.5,
    paddingHorizontal: 15,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 16,
    elevation: 2,
  },
  selectionContainer: {
    flex: 1,
  },
  selectedValue: {
    fontSize: 15,
    textAlign: "left",
  },
  dropdown: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    borderRadius: 8,
    borderWidth: 2,
    overflow: "hidden",
  },
  option: {
    padding: 8,
    paddingHorizontal: 0,
    marginHorizontal: 2
  },
  optionText: {
    fontSize: 15,
    fontWeight: 400,
    marginLeft: 12,
    textTransform: "capitalize",
    fontFamily:AppFonts.HelixaBold
  },
  selectedOption: {
    borderRadius: 6,
    elevation: 2
  },
  scrollbarTrack: {
    position: "absolute",
    right: 2,
    top: 0,
    bottom: 0,
    width: 4,
    borderRadius: 2,
  },
  scrollbarThumb: {
    position: "absolute",
    width: 4,
    borderRadius: 2,
  },
  errorText:{
    marginLeft:8,
    fontSize:12,
    fontFamily:AppFonts.HelixaBold,
  },
});

export default SelectBox;
