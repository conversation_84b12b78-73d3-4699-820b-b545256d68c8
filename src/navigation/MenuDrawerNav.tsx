import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import HomeScreen from '../screens/Home/HomeScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import InventoryScreen from '../screens/Inventory/InventoryScreen';
import { useTheme } from '../context/ThemeContext';
import CaretakerInfoScreen from '../screens/Caretaker/CaretakerInfoScreen';
import PrivacyScreen from '../screens/PrivacyAndHelp/PrivacyScreen';
import HelpScreen from '../screens/PrivacyAndHelp/FAQScreen';
import SoftwareUpdate from '../screens/Software/SoftwareUpdateScreen';
import TabletHistoryScreen from '../screens/Tablet/TabletHistoryScreen';
import CustomDrawerContent from './components/Drawer/CustomDrawerContent';

const Drawer = createDrawerNavigator();

function MyDrawer() {
  const { theme } = useTheme();
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.background.primary,
          width: '80%',
          overflow: 'hidden',
          borderTopStartRadius: 24,
          borderBottomStartRadius: 24,
        },
        drawerPosition: 'right',
      }}
    >
      <Drawer.Screen
        name="Homescreen"
        component={HomeScreen}
      />
      <Drawer.Screen
        name="Personal Information"
        component={ProfileScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="Caretaker Info"
        component={CaretakerInfoScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="Inventory"
        component={InventoryScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="TabletHistory"
        component={TabletHistoryScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="Software Update"
        component={SoftwareUpdate}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="Privacy"
        component={PrivacyScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />
      <Drawer.Screen
        name="Help & Support"
        component={HelpScreen}
        options={{
          drawerItemStyle: { display: 'none' }
        }}
      />

    </Drawer.Navigator>
  );
}

export default MyDrawer;