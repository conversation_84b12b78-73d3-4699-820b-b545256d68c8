import { createNativeStackNavigator } from "@react-navigation/native-stack";
import MyDrawer from "./MenuDrawerNav";
import HomeScreen from "../screens/Home/HomeScreen";
import ProfileScreen from "../screens/Profile/ProfileScreen";
import InventoryScreen from "../screens/Inventory/InventoryScreen";
import TabletHistoryScreen from "../screens/Tablet/TabletHistoryScreen";
import TabletTrackingScreen from "../screens/Tablet/TabletTrackingScreen";
import SoftwareUpdate from "../screens/Software/SoftwareUpdateScreen";
import PrivacyScreen from "../screens/PrivacyAndHelp/PrivacyScreen";
import HelpScreen from "../screens/PrivacyAndHelp/FAQScreen";
import CaretakerInfoScreen from "../screens/Caretaker/CaretakerInfoScreen";
import NutrientSummaryScreen from "../screens/Nutrients/NutrientSummaryScreen";
import TabletAllDetailsScreen from "../screens/Tablet/TabletAllDetailsScreen";
import TabletSummaryScreen from "../screens/Tablet/TabletSummaryScreen";

export type MainStackParamList = {
  Home: undefined;
  "Personal Information": undefined;
  "Caretaker Info": undefined;
  Inventory: undefined;
  "Tablet History": undefined;
  "Tablet Tracking":{cartridgeModalId:string},
  "Tablet All Details": {cartridgeModalId:string};
  "Tablet Summary": undefined;
  "Nutrient Summary": undefined;
  "Software Update": undefined;
  Privacy: undefined;
  "Help & Support": undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
      initialRouteName="Home"
    >
      <Stack.Screen
        name="Home"
        component={HomeScreen}
        options={{
          animation: "slide_from_left",
        }}
      />
      <Stack.Screen name="Personal Information" component={ProfileScreen} />
      <Stack.Screen name="Caretaker Info" component={CaretakerInfoScreen} />
      <Stack.Screen name="Inventory" component={InventoryScreen} />
      <Stack.Screen name="Tablet History" component={TabletHistoryScreen} />
      <Stack.Screen name="Tablet Tracking" component={TabletTrackingScreen} />
      <Stack.Screen name="Tablet All Details" component={TabletAllDetailsScreen} />
      <Stack.Screen name="Tablet Summary" component={TabletSummaryScreen} />
      <Stack.Screen name="Nutrient Summary" component={NutrientSummaryScreen} />
      <Stack.Screen name="Software Update" component={SoftwareUpdate} />
      <Stack.Screen name="Privacy" component={PrivacyScreen} />
      <Stack.Screen name="Help & Support" component={HelpScreen} />
    </Stack.Navigator>
  );
};

export default MainStack;
