import { Dimensions, StyleSheet, View, Text } from 'react-native'
import React from 'react'
import { Bar<PERSON>hart } from 'react-native-gifted-charts'
import { useTheme } from '../../../context/ThemeContext'
import { AppFonts } from '../../../constants/theme/fonts/fonts'

const SixMonthTabletGraph = () => {
  const { theme } = useTheme()

  // Generate sample data for 6 months with percentage values
  const generateMonthData = (monthLabel: string, percentage: number) => {
    let frontColor = 'transparent'
    let gradientColor = 'transparent'
    let capColor = '#9CA3AF'

    // Assign colors based on percentage ranges
    if (percentage >= 5) {
      frontColor = 'transparent'
      gradientColor = 'transparent'
      capColor = '#9EC248'
    } else if (percentage >= 3) {
      frontColor = 'transparent'
      gradientColor = 'transparent'
      capColor = '#F16522'
    }

    return {
      value: percentage,
      label: monthLabel,
      frontColor,
      gradientColor,
      capColor,
      topLabelComponent: () => (
        <Text style={{
          color: theme.text.quinary,
          fontSize: 10,
          fontFamily:AppFonts.HelixaBold,
          marginBottom: 2,
          textAlign: 'center'
        }}>
          {percentage} tab avg
        </Text>
      ),
    }
  }

  // Create data for 6 months as shown in the UI design
  const barData = [
    generateMonthData('Jan', 1),
    generateMonthData('Feb', 2),
    generateMonthData('Mar', 0),
    generateMonthData('Apr', 5),
    generateMonthData('May', 1),
    generateMonthData('Jun', 4),
  ]

  return (
    <View style={styles.container}>
      <BarChart
        data={barData}
        width={Dimensions.get('window').width-96}
        height={160}
        cappedBars
        barWidth={(Dimensions.get('window').width-94)/barData.length}
        spacing={0}
        roundedTop={false}
        roundedBottom={false}
        hideRules={false}
        showVerticalLines={true}
        xAxisThickness={0}
        yAxisThickness={0}
        yAxisTextStyle={{
          color: theme.text.quinary,
          fontFamily: AppFonts.HelixaBold,
          fontSize: 10,
        }}
        xAxisLabelTextStyle={{
          color: theme.text.quinary,
          fontFamily: AppFonts.HelixaBold,
          fontSize: 10,
          textAlign: 'center',
        }}
        noOfSections={4}
        maxValue={8}
        stepValue={2}
        initialSpacing={-4}
        barBorderRadius={4}
        adjustToWidth={true}
        endSpacing={-10}
        rulesColor={'#CCCCCC'}
        rulesType="solid"
        verticalLinesColor={'#CCCCCC'}
        verticalLinesThickness={1}
        showReferenceLine1={false}
        showReferenceLine2={false}
        showReferenceLine3={false}
        backgroundColor="transparent"
        disableScroll={true}
        showGradient={true}
        scrollToEnd={false}
        showScrollIndicator={false}
      />
    </View>
  )
}

export default SixMonthTabletGraph

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginLeft: -24,
  },
})