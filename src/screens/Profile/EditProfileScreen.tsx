import { Image, Linking, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import IconBtn from '../../components/Buttons/IconBtn'
import { Ionicons, MaterialIcons } from '@expo/vector-icons'
import { useTheme } from '../../context/ThemeContext'
import ActivityLevelEnum from '../Onboarding/enums/ActivityLevelEnum'
import InputBoxWithLabel from '../../components/FormFields/InputBoxWithLabel'
import SelectBoxWithText from '../../components/FormFields/SelectBoxWithText';
import GenderEnum from '../Onboarding/enums/GenderEnum'
import Toast from 'react-native-toast-message'
import { getAddressFromCoordinates, getUserLocation } from '../../utils/getUserLocation'
import DOBPickerWithLabel from '../../components/FormFields/DOBPickerWithText'
import * as ImagePicker from "expo-image-picker";
import ImagePermissionModal from '../../components/Modals/Image/ImagePermissionModal'
import CustomModal from '../../components/Modals/CustomModal'
import SimpleBtn from '../../components/Buttons/SimpleBtn'
import PageLoader from '../../components/Loaders/PageLoader'

const ProfilePicPlacehlder = require('../../../assets/profile-pic-placeholder.jpg')

const genderOptions = [
    { label: "Male", value: GenderEnum.MALE },
    { label: "Female", value: GenderEnum.FEMALE },
    { label: "Other", value: GenderEnum.OTHER }
]

type ValidationErrorType = {
    firstName?: string,
    lastName?: string,
    email?: string,
    dob?: string,
    gender?: string,
    city?: string,
    state?: string,
    height?: string,
    weight?: string,
    activityLevel?: string
}

interface EditProfileScreenProps {
    localData: {
        image:string,
        firstName: string;
        lastName: string;
        email: string;
        dob?: string;
        gender?: string;
        city?: string | null;
        state?: string | null;
        country?: string | null;
        height?: number | string;
        weight?: number | string;
        activityLevel?: string;
    },
    setLocalData:React.Dispatch<React.SetStateAction<{
        image:string,
        firstName: string;
        lastName: string;
        email: string;
        dob?: string;
        gender?: string;
        city?: string | null;
        state?: string | null;
        country?: string | null;
        height?: number | string;
        weight?: number | string;
        activityLevel?: string;
    }>>,
    currentDropdownId: string | null;
    setCurrentDropdownId: (id: string | null) => void;
    scrollToOffset?: (y: number) => void;
    validationError: ValidationErrorType;
    setValidationError: React.Dispatch<React.SetStateAction<ValidationErrorType>>;
}

const EditProfileScreen = ({
    localData,
    setLocalData,
    currentDropdownId,
    setCurrentDropdownId,
    scrollToOffset,
    validationError,
    setValidationError,
}: EditProfileScreenProps) => {
    const { theme } = useTheme();

    const [isFetchingLocation, setIsFetchingLocation] = useState(false);
    const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);

    const [ showImagePermissionModal, setShowImagePermissionModal ] = useState(false);

    const fetchUserLocation = async () => {
        setIsFetchingLocation(true);
        const rawLocation = await getUserLocation();
        if (rawLocation.success) {
          const { data: location } = rawLocation;
          if (!location) return;
    
          const { latitude, longitude } = location.coords;
          const rawAddress = await getAddressFromCoordinates(latitude, longitude);
          if (rawAddress.success) {
            const { data: address } = rawAddress;
    
            setLocalData(prev => ({
              ...prev,
              city: address.city,
              state: address.region,
              country: address.country
            }))
            setIsFetchingLocation(false);
          }
          else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: rawAddress.error.message
            });
            setIsLocationModalVisible(true);
          }
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: rawLocation.error.message
          });
          setIsLocationModalVisible(true);
        }
        setIsFetchingLocation(false);
    }

    const handleOpenSetting = () => {
        Linking.openSettings();
        setIsLocationModalVisible(false);
    }

    const pickImage = async () => {
        // Request permission
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (status !== "granted") {
            setShowImagePermissionModal(true);
            return;
        }

        // Launch image picker
        let result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        });

        if (!result.canceled) {
            setLocalData(prev => ({
                ...prev,
                image: result.assets[0].uri
            }));
        }
    };

    return (
        <View style={styles.container}>
            <TouchableOpacity onPress={pickImage} activeOpacity={.8}>
                <View style={styles.profilePicContainer}>
                    <Image 
                        source={localData.image ? { uri: localData.image } : ProfilePicPlacehlder} 
                        style={styles.profilePic} 
                    />
                    <IconBtn
                        icon={<MaterialIcons name="edit" size={24} color={theme.icon.secondary} />}
                        onPress={pickImage}
                        containerStyle={{
                            padding:6,
                            bottom: "0%",
                            right: 0,
                            left:"auto",
                            top:"auto",
                            transform: [{ translateY: 0 }, { translateX: '-50%' }] 
                        }}
                    />
                </View>
            </TouchableOpacity>
            <View style={styles.userDetailContainer}>
                <InputBoxWithLabel
                    label='First Name'
                    placeholder='First Name'
                    selected={localData.firstName}
                    setSelected={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            firstName: value
                        }))
                    }}
                    error={validationError?.firstName}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                            ...prev,
                            firstName: undefined
                        }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
                <InputBoxWithLabel
                    label='Last Name'
                    placeholder='Last Name'
                    selected={localData.lastName}
                    setSelected={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            lastName: value
                        }))
                    }}
                    error={validationError?.lastName}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                            ...prev,
                            lastName: undefined
                        }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
            </View>

            <View style={styles.userDetailContainer}>
                <DOBPickerWithLabel
                    value={localData.dob?.split('-').reverse().join('-')}
                    label='Date of Birth'
                    placeholder="Choose date"
                    onSelectDate={(date, age) => {
                        setLocalData(prev => ({
                        ...prev,
                        dob: date.split('-').reverse().join('-')
                        }))
                    }}
                    error={validationError?.dob}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                        ...prev,
                        dob: undefined
                        }))
                    }}
                    wrapperStyle={{
                        flex: 1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
                <InputBoxWithLabel
                    label='Email'
                    placeholder='Email'
                    selected={localData.email}
                    setSelected={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            email: value
                        }))
                    }}
                    error={validationError?.email}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                            ...prev,
                            email: undefined
                        }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
            </View>
                  
            <View style={styles.userDetailContainer}>
                <InputBoxWithLabel
                    numericOnly
                    label='Height'
                    placeholder='125 (cm)'
                    selected={localData.height ? localData.height.toString() : ""}
                    setSelected={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            height: Number(value)
                        }))
                    }}
                    error={validationError?.height}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                            ...prev,
                            height: undefined
                        }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
                <InputBoxWithLabel
                    numericOnly
                    label='Weight'
                    placeholder='50 (kg)'
                    selected={localData.weight ? localData.weight.toString() : ""}
                    setSelected={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            weight: Number(value)
                        }))
                    }}
                    error={validationError?.weight}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                            ...prev,
                            weight: undefined
                        }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
                <SelectBoxWithText
                    label='Gender'
                    placeholder='Choose gender'
                    options={genderOptions}
                    selectedValue={localData.gender}
                    onValueChange={(value) => {
                        setLocalData(prev => ({
                            ...prev,
                            gender: value as GenderEnum
                        }))
                    }}
                    error={validationError?.gender}
                    clearValidationError={() => {
                        setValidationError((prev: ValidationErrorType) => ({
                        ...prev,
                        gender: undefined
                        }))
                    }}
                    dropdownId={"gender"}
                    setCurrentOpenDropdown={(id) => {
                        setCurrentDropdownId(id);
                    }}
                    maxOptionsToShow={3}
                    changeBG={true}
                    currentOpenDropdown={currentDropdownId}
                    triggerZ={8}
                    listZ={9}
                    wrapperStyle={{
                        flex: 1
                    }}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    selectBoxStyle={{
                        borderRadius:10
                    }}
                    optionsBorderRadius={10}
                    scrollToOffset={scrollToOffset}
                />
            </View>
            <View style={styles.userDetailContainer}>
                <InputBoxWithLabel
                    label='City'
                    selected={localData.city ? localData.city : ''}
                    placeholder='New Delhi'
                    setSelected={(value) => {
                    setLocalData(prev => ({
                        ...prev,
                        city: value
                    }))
                    }}
                    error={validationError?.city}
                    clearValidationError={() => {
                    setValidationError((prev: ValidationErrorType) => ({
                        ...prev,
                        city: undefined
                    }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    loading={isFetchingLocation}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />

                <InputBoxWithLabel
                    label='State'
                    selected={localData.state ? localData.state : ''}
                    placeholder='Delhi'
                    setSelected={(value) => {
                    setLocalData(prev => ({
                        ...prev,
                        state: value
                    }))
                    }}
                    error={validationError?.state}
                    clearValidationError={() => {
                    setValidationError((prev: ValidationErrorType) => ({
                        ...prev,
                        state: undefined
                    }))
                    }}
                    containerStyle={{
                        flex:1
                    }}
                    loading={isFetchingLocation}
                    icon={<MaterialIcons name="my-location" size={24} color={theme.icon.primary} />}
                    onIconPress={fetchUserLocation}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    inputContainerWrapperStyle={{
                        borderRadius:10
                    }}
                />
            </View>

            <View style={styles.userDetailContainer}>
                <SelectBoxWithText
                    label='Activity level'
                    placeholder='Choose activity level'
                    options={[
                        { label: "Sedentary", value: String(ActivityLevelEnum.SEDENTARY) },
                        { label: "Lightly active", value: String(ActivityLevelEnum.LIGHTLY_ACTIVE) },
                        { label: "Moderately active", value: String(ActivityLevelEnum.MODERATELY_ACTIVE) },
                        { label: "Very active", value: String(ActivityLevelEnum.VERY_ACTIVE) },
                        { label: "Extremely active", value: String(ActivityLevelEnum.EXTREMELY_ACTIVE) }
                    ]}
                    selectedValue={localData.activityLevel}
                    onValueChange={(value) => {
                    setLocalData(prev => ({
                        ...prev,
                        activityLevel: value as ActivityLevelEnum
                    }))
                    }}
                    error={validationError?.activityLevel}
                    clearValidationError={() => {
                    setValidationError((prev: ValidationErrorType) => ({
                        ...prev,
                        activityLevel: undefined
                    }))
                    }}
                    wrapperStyle={{
                        flex:1
                    }}
                    dropdownId={"activityLevel"}
                        setCurrentOpenDropdown={(id) => {
                        setCurrentDropdownId(id);
                    }}
                    maxOptionsToShow={5}
                    changeBG={true}
                    currentOpenDropdown={currentDropdownId}
                    triggerZ={6}
                    listZ={7}
                    labelColor='quinary'
                    labelStyle={{
                        fontSize:12
                    }}
                    selectBoxStyle={{
                        borderRadius:10
                    }}
                    optionsBorderRadius={10}
                    scrollToOffset={scrollToOffset}
                />
                <ImagePermissionModal
                    visible={showImagePermissionModal}
                    onClose={() => setShowImagePermissionModal(false)}
                />
                
                <CustomModal
                    visible={isLocationModalVisible}
                    onClose={() => setIsLocationModalVisible(false)}
                    title="Location"
                    subtitle="Permission"
                    icon={
                    <Ionicons name="location-sharp" size={120} color={theme.icon.primary}
                        style={{
                        alignSelf: "center"
                        }}
                    />
                    }
                    desc="Please grant location permission to continue"
                    buttons={
                    <View style={styles.locationModalBtnsContainer}>
                        <SimpleBtn
                            title='Open settings'
                            onPress={handleOpenSetting}
                            containerStyle={{
                                flex: 1,
                            }}
                        />
                    </View>
                    }
                />
            </View>
        </View>
    )
}

export default EditProfileScreen

const styles = StyleSheet.create({
    container:{
        flex:1,
    },
    profilePicContainer:{
        width:'50%',
        height:"auto",
        aspectRatio:1,
        alignSelf:"center",
    },
    profilePic:{
        width:"100%",
        height:"100%",
        borderRadius:1000,
        resizeMode:"cover"
    },
    userDetailContainer:{
        marginTop:16,
        flexDirection:"row",
        gap:16,
        marginBottom:2
    },
    locationModalBtnsContainer: {
        flexDirection: "row",
        gap: 16,
    }
})