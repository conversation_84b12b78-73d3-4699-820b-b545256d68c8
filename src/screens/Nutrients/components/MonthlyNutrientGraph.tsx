import { Dimensions, StyleSheet, View } from 'react-native'
import React from 'react'
import { Bar<PERSON><PERSON> } from 'react-native-gifted-charts'
import { useTheme } from '../../../context/ThemeContext'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import FadePattern from '../../../components/Graphs/FadePattern'

const MonthlyNutrientGraph = () => {
  const { theme } = useTheme()

  // Generate sample data for 5 months with multiple bars per month
  const generateMonthData = (monthLabel: string, daysInMonth: number) => {
    const monthData = []

    // Days that should show labels
    const labelDays = [1, 7, 15, 22, 30]

    // Generate data for each day in the month
    for (let day = 1; day <= daysInMonth; day++) {
      let frontColor = '#C2C2C2'

      // Randomly assign colors to simulate different tablet intake patterns
      const random = day % 3
      if (random >= 2) {
        frontColor = '#9EC248'
      } else if (random >= 1) {
        frontColor = '#F16522'
      }

      // Only show label if day is in labelDays array
      let label = ''
      if (labelDays.includes(day)) {
        if (day === 1) {
          label = '1st'
        } else if (day === 22) {
          label = '22nd'
        } else if (day === 30) {
          label = '30th'
        } else {
          label = `${day}th`
        }
      }

      const data: any = {
        value: ((day%8) * 10), // Random value between 1-8
        frontColor,
        patternId: 'WhiteFadePattern',
      }

      if(label){
        data.label = label
      }

      if(day === 1){ 
        data.labelTextStyle={
          width:50,
          fontSize:10,
          fontFamily:AppFonts.HelixaBold,
          color: theme.text.quinary,
          textAlign:'justify',
          marginLeft:4
        }
      }

      if(day === 30){
        data.labelTextStyle={
          width:50,
          fontSize:10,
          fontFamily:AppFonts.HelixaBold,
          color: theme.text.quinary,
          textAlign:'left',
          right:16
        }
      }


      if (day === daysInMonth) {
        data.spacing = 0
      }

      monthData.push(data)
    }

    return monthData
  }

  // Create data for 5 months as shown in the UI
  const barData = [
    ...generateMonthData('Jul', 30)
  ]

  return (
    <View style={styles.container}>
      <BarChart
        data={barData}
        width={Dimensions.get('window').width - 96}
        height={160}
        barWidth={(Dimensions.get('window').width - 94) / (barData.length * 2)} // Smaller bars to fit more data
        spacing={(Dimensions.get('window').width - 94) / (barData.length * 2)} // Tight spacing between bars
        roundedTop={false}
        roundedBottom={false}
        hideRules={false} // Show horizontal grid lines
        showVerticalLines={true} // Show vertical grid lines
        verticalLinesColor={'#CCCCCC'} // Vertical grid color (more visible)
        verticalLinesThickness={1} // Vertical grid line thickness
        xAxisThickness={0} // Hide X-axis line
        yAxisThickness={0} // Hide Y-axis line
        yAxisTextStyle={{
          color: theme.text.quinary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
        }}
        xAxisLabelTextStyle={{
          color: theme.text.quinary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
          width:50,
          textAlign:'justify',
        }}
        noOfSections={4}
        maxValue={100}
        stepValue={25}
        initialSpacing={0}
        barBorderRadius={2}
        adjustToWidth={false} // Don't adjust to width to maintain consistent bar sizes
        endSpacing={-4}
        rulesColor={'#CCCCCC'} // Horizontal grid line color (more visible)
        rulesType="solid"
        showReferenceLine1={false}
        showReferenceLine2={false}
        showReferenceLine3={false}
        backgroundColor="transparent"
        // isAnimated
        disableScroll={false} // Enable scroll for monthly view
        patternId='WhiteFadePattern'
        barBackgroundPattern={FadePattern}
        scrollToEnd={false}
        showScrollIndicator={false}
        yAxisLabelSuffix='%'
      />
    </View>
  )
}

export default MonthlyNutrientGraph

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start', // Align to start to allow horizontal scrolling
    marginLeft: -24,
  },
})