import { Dimensions, StyleSheet, View } from 'react-native'
import React from 'react'
import { Bar<PERSON><PERSON> } from 'react-native-gifted-charts'
import { useTheme } from '../../../context/ThemeContext'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { Defs, LinearGradient, Path, Pattern, Rect, Stop } from 'react-native-svg'
import FadePattern from '../../../components/Graphs/FadePattern'

const WeeklyNutrientsGraph = () => {
  const { theme } = useTheme()

  // Sample data matching the UI design with gradients
  const barData = [
    {
      value: 60,
      label: 'Mon',
      frontColor: '#E0E0E0', // Gray color (100% opacity)
      // gradientColor: '#C2C2C2', // Gray color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 70,
      label: 'Tue',
      frontColor: '#E0E0E0', // Gray color (100% opacity)
      // gradientColor: '#C2C2C2', // Gray color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 50,
      label: 'Wed',//FFC4A3
      frontColor: '#F16522', // Orange color (100% opacity)
      // gradientColor: '#F16522', // Orange color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 80,
      label: 'Thurs',
      frontColor: '#9EC248', // Green color (100% opacity)
      // gradientColor: '#9EC248', // Green color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 40,
      label: 'Fri',
      frontColor: '#E0E0E0', // Gray color (100% opacity)
      // gradientColor: '#C2C2C2', // Gray color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 60,
      label: 'Sat',
      frontColor: '#E0E0E0', // Gray color (100% opacity)
      // gradientColor: '#C2C2C2', // Gray color (20% opacity)
      patternId: 'WhiteFadePattern',
    },
    {
      value: 60,
      label: 'Sun',
      frontColor: '#E0E0E0', // Gray color (100% opacity)
      // gradientColor: '#C2C2C2', // Gray color (20% opacity)
      patternId: 'WhiteFadePattern',
      spacing:0
    },
  ]

  return (
    <View style={styles.container}>
      <BarChart
        data={barData}
        width={Dimensions.get('window').width-96}
        height={160}
        barWidth={(Dimensions.get('window').width-100)/(barData.length*2)}
        spacing={(Dimensions.get('window').width-100)/((barData.length-1)*2)}
        roundedTop={false}
        roundedBottom={false}
        hideRules={false} // Show horizontal grid lines
        showVerticalLines={true} // Show vertical grid lines
        verticalLinesColor={'#CCCCCC'} // Vertical grid color (more visible)
        verticalLinesThickness={1} // Vertical grid line thickness
        hideYAxisText={false} // Keep Y-axis text to ensure grid shows
        xAxisThickness={0} // Hide X-axis line
        yAxisThickness={0} // Hide Y-axis line
        yAxisTextStyle={{
          color: theme.text.quinary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
        }}
        xAxisLabelTextStyle={{
          color: theme.text.quinary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
          textAlign: 'center',
        }}
        noOfSections={4}
        maxValue={100}
        stepValue={25}
        initialSpacing={0}
        barBorderRadius={4}
        adjustToWidth={true}
        endSpacing={-4}
        rulesColor={'#CCCCCC'} // Horizontal grid line color (more visible)
        rulesType="solid"
        // showReferenceLine1={true}
        // referenceLine1Position={50}
        // referenceLinesOverChartContent={false}
        // referenceLine1Config={{
        //   color: theme.border.primary,
        //   dashWidth: 4,
        //   dashGap: 2,
        //   type: 'dashed',
        // }}
        backgroundColor="transparent"
        // isAnimated
        disableScroll={true}
        // showGradient={true}
        barBackgroundPattern={FadePattern}
        yAxisLabelSuffix='%'
      />
    </View>
  )
}

export default WeeklyNutrientsGraph

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginLeft:-24
  },
})