import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { MainStackParamList } from '../../navigation/MainStack';
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import DateIntervalSelector from '../../components/Graphs/DateIntervalSelector';
import PeriodSelector from '../../components/Graphs/PeriodSelector';
import HorizontalSelector from '../../components/Graphs/HorizontalSelector';
import GraphSummarySection from '../../components/Graphs/GraphSummarySection';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import WeeklyNutrientsGraph from './components/WeeklyNutrientsGraph';
import MonthlyNutrientGraph from './components/MonthlyNutrientGraph';
import SixMonthNutrientGraph from './components/SixMonthNutrientGraph';

type MainStackNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const nutrients = [
  { label: 'Vit D3', value: 'Vitamin D3' },
  { label: 'Cal', value: 'Calcium' },
  { label: 'Iron', value: 'Iron' },
  { label: 'Zinc', value: 'Zinc' },
  { label: 'Mag', value: 'Magnesium' },
  { label: 'Vit C', value: 'Vitamin C' },
  { label: 'Vit E', value: 'Vitamin E' },
  { label: 'Vit A', value: 'Vitamin A' },
  { label: 'Vit B12', value: 'Vitamin B12' },
];

const extracts = [
  { label: 'Elderberry', value: 'Elderberry Extract' },
  { label: 'Echinacea', value: 'Echinacea purpurea Extract' },
  { label: 'Curcuma', value: 'Curcuma longa / Curcuvail' },
  { label: 'Piperine', value: 'Piperine extract (10%)' },
  { label: 'L-Theanine', value: 'L-Theanine' },
];

const weekDays = ['Mon', 'Tue', 'Wed', 'Thurs', 'Fri', 'Sat', 'Sun'];

// Sample data for the bar chart (percentages)
const barData = [45, 50, 70, 90, 48, 52, 58];

const NutrientSummaryScreen = () => {
  const { theme } = useTheme();

  const [startDate, setStartDate] = useState(new Date());
  const [selectedPeriod, setSelectedPeriod] = useState<'Week' | 'Month' | '6 Month'>('Week');
  const [selectedOption, setSelectedOption] = useState('Vitamin D3');
  const [selectedView, setSelectedView] = useState<'Micronutrients' | 'Extracts'>('Micronutrients');

  useEffect(() => {
    if (selectedView === 'Micronutrients') {
      setSelectedOption('Vitamin D3');
    }
    else {
      setSelectedOption('Elderberry Extract');
    }
  }, [selectedView])

  return (
    <AppLayout>
      <View style={styles.wrapper}>
        <ScrollView style={styles.container} contentContainerStyle={styles.containerContent}>
          <View style={styles.cardWrapper}>
            {/* Title Section */}
            <View style={styles.titleSection}>
              <TouchableOpacity
                activeOpacity={1}
                style={[
                  styles.viewButton,
                  selectedView === 'Micronutrients' && styles.viewButtonActive,
                  selectedView === 'Micronutrients' && { backgroundColor: theme.background.primary }
                ]}
                onPress={() => setSelectedView('Micronutrients')}
              >
                <Text numberOfLines={1} ellipsizeMode='tail' style={[
                  styles.viewButtonText,
                  { color: selectedView === 'Micronutrients' ? theme.text.primary : theme.text.quinary }
                ]}>
                  Micronutrients{' '}
                  <Text style={styles.selectedOption}>
                    {nutrients.filter(nutrient => nutrient.value === selectedOption)?.[0]?.label ? (`(${nutrients.filter(nutrient => nutrient.value === selectedOption)?.[0]?.label})`) : ""}
                  </Text>
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                activeOpacity={1}
                style={[
                  styles.viewButton,
                  selectedView === 'Extracts' && styles.viewButtonActive,
                  selectedView === 'Extracts' && { backgroundColor: theme.background.primary }
                ]}
                onPress={() => setSelectedView('Extracts')}
              >
                <Text numberOfLines={1} ellipsizeMode='tail' style={[
                  styles.viewButtonText,
                  { color: selectedView === 'Extracts' ? theme.text.primary : theme.text.quinary }
                ]}>
                  Extracts{' '}
                  <Text style={styles.selectedOption}>
                    {extracts.filter(extract => extract.value === selectedOption)?.[0]?.label ? (`(${extracts.filter(extract => extract.value === selectedOption)?.[0]?.label})`) : ""}
                  </Text>
                </Text>
              </TouchableOpacity>
            </View>

            <View style={[styles.card, {
              backgroundColor: theme.background.primary,
              borderTopLeftRadius: selectedView === 'Micronutrients' ? 0 : 20,
              borderTopRightRadius: selectedView === 'Extracts' ? 0 : 20,
            }]}>
              {/* Date Range Header */}
              <View style={styles.dateRangeHeader}>
                <DateIntervalSelector
                  startDate={startDate}
                  setStartDate={setStartDate}
                  selectedPeriod={selectedPeriod}
                />
              </View>

              <View style={styles.graphDetailsWrapper}>
                {/* Nutrient Selector */}
                <HorizontalSelector
                  options={selectedView === 'Micronutrients' ? nutrients : extracts}
                  selectedOption={selectedOption}
                  setSelectedOption={setSelectedOption}
                />

                {selectedPeriod === 'Week' && <WeeklyNutrientsGraph />}
                {selectedPeriod === 'Month' && <MonthlyNutrientGraph />}
                {selectedPeriod === '6 Month' && <SixMonthNutrientGraph />}

                {/* Summary Section */}
                <View style={[styles.graphSummarySection, { borderTopColor: theme.border.primary }]}>
                  <GraphSummarySection
                    percentage={74}
                    highlights={[
                      "Over this week, your Immunity score is lower than your prior 7-days average (65%).",
                      "Try to improve your intake to strengthen your Immunity."
                    ]}
                  />
                </View>

                {/* Period Selector */}
                <PeriodSelector
                  selectedPeriod={selectedPeriod}
                  setSelectedPeriod={setSelectedPeriod}
                />
              </View>
            </View>
          </View>
        </ScrollView>
        <SimpleBtn
          title='Download PDF'
          onPress={() => { }}
          containerBgColor='primary'
          titleTextColor='secondary'
          containerStyle={styles.downloadBtn}
        />
      </View>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    marginBottom: 90,
  },
  container: {
    flex: 1,
  },
  containerContent: {
    flexGrow: 1,
    padding: 8,
    gap: 12,
  },
  cardWrapper: {
    // gap: 8,
    //  elevation: 10,
    // backgroundColor:'red'
  },
  titleSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewButton: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingBottom: 4
  },
  viewButtonActive: {
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // elevation: 3,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  viewButtonText: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaBold,
    textAlign: "center"
  },
  selectedOption: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
  },
  card: {
    borderRadius: 20,
    padding: 16,
    paddingHorizontal: 20,
    paddingTop: 8,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 8,
    // elevation: 3,
  },
  dateRangeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateRangeText: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
  },
  graphDetailsWrapper: {
    gap: 16,
  },
  chartContainer: {
    paddingVertical: 20,
  },
  barsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 150,
  },
  barColumn: {
    flex: 1,
    alignItems: 'center',
    gap: 8,
  },
  barContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    paddingHorizontal: 4,
  },
  bar: {
    width: '100%',
    borderRadius: 8,
    minHeight: 20,
  },
  dayLabel: {
    fontSize: 11,
    fontFamily: AppFonts.HelixaRegular,
    textAlign: 'center',
  },
  graphSummarySection: {
    paddingVertical: 4,
    borderTopWidth: 1,
    paddingTop: 20,
  },
  downloadBtn: {
    width: "auto",
    alignSelf: 'center',
    paddingHorizontal: 24
  }
});

export default NutrientSummaryScreen;