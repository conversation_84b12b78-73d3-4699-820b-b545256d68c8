
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PermissionsAndroid, Platform } from 'react-native';
import { BleManager, Characteristic, Device, Subscription } from 'react-native-ble-plx';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { CH_SERVICE_UUID, RX_UUID, SERVICE_UUIDS, TX_UUID } from '../constants/device/DeviceServiceUUIDS';
import Toast from 'react-native-toast-message';
import { SNBCommandService } from '../services/Bluetooth/SMPCommandService';
import { SYNC_RTC_COMMAND_ID } from '../constants/device/DeviceCommandIDS';
import cartridgeService from '../apis/cartridge/cartridgeService';
import dispenseService from '../apis/dispense/dispenseService';
import useDispenseStore from './DispenseStore';
import safeParseNumber from '../utils/safeParseNumber';

type BluetoothStoreType = {
    bleManager: BleManager,
    isBluetoothConnected: boolean,
    setIsBluetoothConnected: (isBluetoothConnected: boolean) => void,

    isBluetoothPermissionModalVisible: boolean,
    isLocationPermissionModalVisible: boolean,
    setIsLocationPermissionModalVisible: (isLocationPermissionModalVisible: boolean) => void,
    setIsBluetoothPermissionModalVisible: (isBluetoothPermissionModalVisible: boolean) => void,

    requestBluetoothPermission: (showBluetoothPermissionModal?: boolean, showLocationPermissionModal?: boolean) => Promise<boolean>,
    

    scanningDevices: boolean,
    refreshingDevices: boolean,
    devices: Map<string, Device>,
    showScannedDevices: boolean,
    setShowScannedDevices: (showScannedDevices: boolean) => void,
    startDevicesScan: ({ isRefreshing }: { isRefreshing?: boolean }) => void,
    handleGetAllDevices: () => Promise<void>,

    connectingDevice: boolean,
    connectedDevice: Device | null,
    isConnected: boolean,
    connectDevice: (device: Device) => Promise<void>,
    deviceId: string,
    deviceName: string,
    localDeviceName: string,
    batteryPercentage:number,
    editConnectDeviceLocalName: (name: string) => void,
    disconnectDevice: () => Promise<void>,
    removeDevice: () => Promise<void>,
    validateConnectionState: () => Promise<void>,
    reconnectDevice: () => Promise<void>,

    txSubscription: Subscription | null,
    rxSubscription: Subscription | null,
    startSNBResponseMonitoring: () => void,
    stopSNBResponseMonitoring: () => void,

    sendCommand: (
        command: Uint8Array,
        commandName: string,
        expectedCommandId: number,
        failureCallback?: () => void,
        afterSuccessCallback?: () => void
    ) => Promise<Characteristic>,
    getDataFrequency: () => Promise<Characteristic>,
    setDataFrequency: (intervalSeconds: number) => Promise<Characteristic>,

    cartrigeId1: string,
    loadingCartridgeId1Details: boolean,
    cartrigeId1Details: any,
    cartrigeId2: string,
    loadingCartridgeId2Details: boolean,
    cartrigeId2Details: any,
    fetchCartridgeDetails: (cartridgeId: string) => Promise<void>,

    dispensingTabletId: string |null,
    isDispensingTablet: boolean,
    isDispensingInProgress: boolean,
    isSyncingDispenseWithServer: boolean,
    isDispensingSuccess: boolean,
    isDispensingError: boolean,
    dispensingProgress: number,

    setDispensingTabletId: (cartridgeId: string|null) => void,
    resetDispensingState: () => void,
    dispenseNutrition: () => Promise<Characteristic | null> ,
    syncDispenseWithServer: (cartridgeId: string) => Promise<void>,
    setDispensingProgress: (action: number | ((prev: number) => number)) => void,
    getTimeStamp: () => Promise<Characteristic>,
    setTimeStamp: () => Promise<Characteristic>,
    getLiveSensorData: () => Promise<Characteristic>,

    showScheduleTabletModalId: string | null,
    setScheduleTabletModal: (showScheduleTabletModal: string | null) => void,
    
}

const useBluetoothStore = create<BluetoothStoreType>()(
    persist(
        (set, get) => ({
            bleManager: new BleManager(),
            isBluetoothConnected: false,
            setIsBluetoothConnected: (isBluetoothConnected: boolean) => {
                set({ isBluetoothConnected });
            },

            isBluetoothPermissionModalVisible: false,
            isLocationPermissionModalVisible: false,
            setIsLocationPermissionModalVisible: (isLocationPermissionModalVisible: boolean) => {
                set({ isLocationPermissionModalVisible });
            },
            setIsBluetoothPermissionModalVisible: (isBluetoothPermissionModalVisible: boolean) => {
                set({ isBluetoothPermissionModalVisible });
            },

            requestBluetoothPermission: async (showBluetoothPermissionModal: boolean = true, showLocationPermissionModal: boolean = true) => {
                if (Platform.OS === 'ios') {
                    return true
                }
                if (Platform.OS === 'android' && PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION) {
                    const apiLevel = parseInt(Platform.Version.toString(), 10)

                    if (apiLevel < 31) {
                        const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
                        return granted === PermissionsAndroid.RESULTS.GRANTED
                    }
                    if (PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN && PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT) {
                        const result = await PermissionsAndroid.requestMultiple([
                            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
                        ])

                        if (result['android.permission.BLUETOOTH_CONNECT'] !== PermissionsAndroid.RESULTS.GRANTED) {
                            if (showBluetoothPermissionModal) {
                                set((state) => ({
                                    ...state,
                                    isBluetoothPermissionModalVisible: true
                                }))
                            }
                            return false;
                        } else {
                            set((state) => ({
                                ...state,
                                isBluetoothPermissionModalVisible: false
                            }))
                        }

                        if (result['android.permission.ACCESS_FINE_LOCATION'] !== PermissionsAndroid.RESULTS.GRANTED) {
                            if (showLocationPermissionModal) {
                                set((state) => ({
                                    ...state,
                                    isLocationPermissionModalVisible: true
                                }))
                            }
                            return false;
                        } else {
                            set((state) => ({
                                ...state,
                                isLocationPermissionModalVisible: false
                            }))
                        }

                        return (
                            result['android.permission.BLUETOOTH_CONNECT'] === PermissionsAndroid.RESULTS.GRANTED &&
                            result['android.permission.BLUETOOTH_SCAN'] === PermissionsAndroid.RESULTS.GRANTED &&
                            result['android.permission.ACCESS_FINE_LOCATION'] === PermissionsAndroid.RESULTS.GRANTED
                        )
                    }
                }

                return false
            },

            scanningDevices: false,
            refreshingDevices: false,
            devices: new Map(),
            showScannedDevices: false,
            setShowScannedDevices: (showScannedDevices: boolean) => {
                set({ showScannedDevices });
            },
            startDevicesScan: ({ isRefreshing = false }: { isRefreshing?: boolean } = {}) => {

                if (!get().isBluetoothConnected) return;


                set((state) => ({
                    ...state,
                    showScannedDevices: true
                }));
                const tempDevices = new Map();

                if (isRefreshing) {
                    set((state) => ({
                        ...state,
                        refreshingDevices: true
                    }));
                } else {
                    set((state) => ({
                        ...state,
                        scanningDevices: true,
                        devices: new Map()
                    }));
                }
                get().bleManager.startDeviceScan(SERVICE_UUIDS, null, (error, device) => {
                    if (error) {
                        console.log("Scan error:", error);
                        return;
                    }

                    if (device && device.id && !get().devices.has(device.id)) {
                        tempDevices.set(device.id, device);
                    }
                });

                // Stop scan after 10 seconds
                setTimeout(() => {
                    get().bleManager.stopDeviceScan();
                    set((state) => ({
                        ...state,
                        scanningDevices: false,
                        refreshingDevices: false,
                        devices: tempDevices
                    }));
                    console.log("Stopped scanning");
                }, 10000);
            },
            handleGetAllDevices: async () => {
                if (!get().isBluetoothConnected) return;
                const granted = await get().requestBluetoothPermission();
                if (!granted) {
                    return;
                }
                get().startDevicesScan({});
            },

            connectingDevice: false,
            connectedDevice: null,
            isConnected: false,
            connectDevice: async (device: Device) => {

                if (!get().isBluetoothConnected) return;
                try {
                    set((state) => ({
                        ...state,
                        connectingDevice: true
                    }));
                    get().bleManager.stopDeviceScan();
                    const connected = await get().bleManager.connectToDevice(device.id, {
                        autoConnect: true
                    });

                    await connected.discoverAllServicesAndCharacteristics();

                    set((state) => ({
                        ...state,
                        connectedDevice: connected,
                        isConnected: true,
                        connectingDevice: false,
                        deviceId: device.id,
                        deviceName: device.name || device.localName || device.id,
                        localDeviceName: get().localDeviceName!== "SNB Device 1" ? get().localDeviceName: device.localName || device.name || device.id
                    }));

                    get().startSNBResponseMonitoring();
                    // get().setDataFrequency(1);
                    get().setTimeStamp();
                    // if(get().dispensingTabletId){
                    //     get().dispenseNutrition();
                    // }

                    connected.onDisconnected((error, disconnectedDevice) => {
                        get().stopSNBResponseMonitoring();
                        
                        if (error) {
                            // Toast.show({
                            //     type: 'error',
                            //     text1: 'Error',
                            //     text2: "Device disconnected with error",
                            //     position: 'bottom'
                            // });
                        } else {
                            // Toast.show({
                            //     type: 'success',
                            //     text1: 'Success',
                            //     text2: "Device disconnected",
                            //     position: 'bottom'
                            // });
                        }
                        set((state) => ({
                            ...state,
                            connectedDevice: null,
                            isConnected: false,
                            txSubscription: null,
                            rxSubscription: null,
                        }));
                    });
                } catch (error) {

                    set((state) => ({
                        ...state,
                        connectingDevice: false
                    }));

                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: "Failed to connect to device",
                        position: 'bottom'
                    });
                }
            },
            deviceId: "",
            deviceName: "SNB Device 1",
            localDeviceName: "SNB Device 1",
            batteryPercentage: 0,
            editConnectDeviceLocalName: (name: string) => {
                set({ localDeviceName: name });
            },
            disconnectDevice: async () => {
                if(get().isDispensingInProgress) return;

                if (!get().isBluetoothConnected || !get().connectedDevice) return;

                try {
                    get().stopSNBResponseMonitoring();
                    await get().bleManager.cancelDeviceConnection(get().connectedDevice?.id || '');
                    set((state) => ({
                        ...state,
                        isConnected: false,
                        showScannedDevices: false,
                        scanningDevices: false,
                        refreshingDevices: false,
                        devices: new Map(),
                        connectedDevice: null,
                        txSubscription: null,
                        rxSubscription: null,
                        isDispensingTablet: false,
                        isDispensingInProgress: false,
                    }));

                    // Toast.show({
                    //     type: 'success',
                    //     text1: 'Success',
                    //     text2: "Device disconnected",
                    //     position: 'bottom'
                    // });
                }
                catch (error) {
                    console.log(error);
                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: "Failed to disconnect device",
                        position: 'bottom'
                    });
                    set((state) => ({
                        isDispensingTablet: false,
                        isDispensingInProgress: false,
                    }));
                }

            },
            removeDevice: async () => {
                if (!get().isBluetoothConnected || !get().connectedDevice || get().isDispensingInProgress) return;

                try {
                    get().stopSNBResponseMonitoring();
                    await get().bleManager.cancelDeviceConnection(get().connectedDevice?.id || '');
                    set((state) => ({
                        ...state,
                        connectedDevice: null,
                        isConnected: false,
                        showScannedDevices: false,
                        scanningDevices: false,
                        refreshingDevices: false,
                        devices: new Map(),
                        deviceId: "",
                        txSubscription: null,
                        rxSubscription: null,
                    }));

                    // Toast.show({
                    //     type: 'success',
                    //     text1: 'Success',
                    //     text2: "Device removed",
                    //     position: 'bottom'
                    // });
                }
                catch (error) {
                    console.log(error);
                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: "Failed to disconnect device",
                        position: 'bottom'
                    });
                    set((state) => ({
                        isDispensingTablet: false,
                        isDispensingInProgress: false,
                    }));
                }

            },
            validateConnectionState: async () => {
                const connectedDevices = await get().bleManager.connectedDevices(SERVICE_UUIDS);

                connectedDevices.some(async (device) => {

                    if (device.id === get().connectedDevice?.id) {
                        const isConnected = await get().bleManager.isDeviceConnected(device.id);
                        if (isConnected) {
                            set((state) => ({
                                ...state,
                                connectedDevice: device,
                                isConnected: true,
                            }));
                            return true;
                        }
                    }
                    return false;
                });
            },
            reconnectDevice: async () => {

                const connectedDeviceID = get().deviceId;

                if (!connectedDeviceID) return;

                get().stopSNBResponseMonitoring();

                try {
                    set((state) => ({
                        ...state,
                        connectingDevice: true,
                    }));

                    get().bleManager.stopDeviceScan();

                    function connectWithTimeout(deviceId: string, ms: number): Promise<Device> {
                        return Promise.race([
                            get().bleManager.connectToDevice(deviceId, { autoConnect: true, timeout: ms }),
                            new Promise<never>((_, reject) =>
                                setTimeout(() => reject(new Error("Connection timeout")), ms)
                            ),
                        ]);
                    }


                    const connected = await connectWithTimeout(connectedDeviceID, 10000);

                    await connected.discoverAllServicesAndCharacteristics();

                    set((state) => ({
                        ...state,
                        isConnected: true,
                        connectedDevice: connected,
                        connectingDevice: false,
                    }));

                    get().startSNBResponseMonitoring();
                    // get().setDataFrequency(1);
                    get().setTimeStamp();

                    connected.onDisconnected((error, disconnectedDevice) => {
                        if (error) {
                            // Toast.show({
                            //     type: 'error',
                            //     text1: 'Error',
                            //     text2: "Device disconnected with error",
                            //     position: 'bottom'
                            // });
                        } else {
                            // Toast.show({
                            //     type: 'success',
                            //     text1: 'Success',
                            //     text2: "Device disconnected",
                            //     position: 'bottom'
                            // });
                        }
                        set((state) => ({
                            ...state,
                            connectedDevice: null,
                            isConnected: false,
                            txSubscription: null,
                            rxSubscription: null,
                        }));
                    });
                } catch (error) {

                    set((state) => ({
                        ...state,
                        connectingDevice: false,
                        connectedDevice: null,
                        isConnected: false,
                        txSubscription: null,
                        rxSubscription: null,
                    }));

                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: "Failed to connect to device",
                        position: 'bottom'
                    });
                }

            },

            txSubscription: null,
            rxSubscription: null,

            startSNBResponseMonitoring: () => {

                const { bleManager, connectedDevice, isConnected } = get();

                if (!isConnected || !connectedDevice) {
                    return null;
                }

                get().stopSNBResponseMonitoring();

                const handleRXCharacteristicUpdate = (characteristic: any) => {
                    if (!characteristic?.value) return;

                    const responseBytes = SNBCommandService.parseResponseFromBase64(characteristic.value);

                    if (!responseBytes) return;

                    const commandId = responseBytes[2];

                    switch (commandId) {
                        case 1:
                            console.log('Data frequency command response received');
                            break;
                        case 2:
                            // Debounce multiple ACK responses - only process if not already syncing
                            if (!get().isSyncingDispenseWithServer) {
                                set({
                                    isDispensingTablet: false,
                                    isDispensingInProgress: false,
                                    isSyncingDispenseWithServer: true,
                                });
                                
                                get().syncDispenseWithServer(get().dispensingTabletId || '');
                            }

                            console.log('Dispense nutrition command ACK received');
                            break;
                        case 3:
                            if (SNBCommandService.validateResponse(responseBytes, SYNC_RTC_COMMAND_ID)) {
                                // Toast.show({
                                //     type: 'success',
                                //     text1: 'Success',
                                //     text2: 'Time synchronized successfully',
                                //     position: 'bottom'
                                // });
                            }
                            break;
                        case 4:
                            console.log('Live sensor data response received');
                            break;
                        default:
                            console.log('Unknown RX command response:', commandId);
                    }
                };

                const handleTXCharacteristicUpdate = async (characteristic: any) => {
                    if (!characteristic?.value) return;

                    try {
                        const txData = SNBCommandService.parseDispenseResponseFromBase64(characteristic.value);

                        if(!txData) return;

                        // txData.cartridge_id = 'A1B1C95F';
                        // txData.catridge_id_2 = 'A2C1B2D1';


                        // txData.battery_percentage = Date.now()%101;
                        // console.log('TX Notification JSON:', txData);

                        // console.log(new Date(txData.timestamp*1000));

                        if(txData?.battery_percentage){
                            set({batteryPercentage: safeParseNumber(txData.battery_percentage)});
                        }

                        if(txData?.cartridge_id !== get().cartrigeId1){
                            set({cartrigeId1: txData?.cartridge_id,loadingCartridgeId1Details: true});
                            await get().fetchCartridgeDetails(txData?.cartridge_id);
                        }

                        // if(txData?.catridge_id_2 !== get().cartrigeId2){
                        //     set({cartrigeId2: txData?.catridge_id_2,loadingCartridgeId2Details: true});
                        //     await get().fetchCartridgeDetails(txData?.catridge_id_2);
                        // }

                        // Check for dispense completion
                        // if (lastDispenseSequence !== null) {
                        //     Toast.show({
                        //         type: 'success',
                        //         text1: 'Dispense Completed',
                        //         text2: `Water quantity: ${txData.water_qty}`,
                        //         position: 'bottom'
                        //     });
                        //     lastDispenseSequence = null;
                        // }

                    } catch (error) {
                        console.error('Failed to parse TX notification JSON:', error);
                    }
                };

                // Monitor TX characteristic
                const txSubscription = bleManager.monitorCharacteristicForDevice(
                    connectedDevice.id,
                    CH_SERVICE_UUID,
                    TX_UUID,
                    (error, characteristic) => {
                        if (error) {
                            if(error.errorCode !== 201 && error.errorCode!==2){
                                console.error('TX Monitoring error:', error);
                            }
                            return;
                        }
                        handleTXCharacteristicUpdate(characteristic);
                    }
                );

                // Monitor RX characteristic
                const rxSubscription = bleManager.monitorCharacteristicForDevice(
                    connectedDevice.id,
                    CH_SERVICE_UUID,
                    RX_UUID,
                    (error, characteristic) => {
                        if (error) {
                            if(error.errorCode !== 201 && error.errorCode!==2){
                                console.error('RX Monitoring error:', error);
                            }
                            return;
                        }
                        handleRXCharacteristicUpdate(characteristic);
                    }
                );

                set((state) => ({
                    ...state,
                    txSubscription,
                    rxSubscription
                }));
            },
            stopSNBResponseMonitoring: () => {
                const { txSubscription, rxSubscription } = get();
                if (txSubscription) {
                    txSubscription.remove();
                }
                if (rxSubscription) {
                    rxSubscription.remove();
                }
                set((state) => ({
                    ...state,
                    txSubscription: null,
                    rxSubscription: null
                }));
            },

            sendCommand: async (command: Uint8Array, commandName: string, expectedCommandId: number,failureCallback?: () => void,afterSuccessCallback?: () => void) => {

                const { bleManager, connectedDevice, isConnected } = get();

                try {
                    if (!isConnected || !connectedDevice) {
                        throw new Error('No device connected');
                    }

                    const base64Command = SNBCommandService.uint8ArrayToBase64(command);

                    const result = await bleManager.writeCharacteristicWithResponseForDevice(
                        connectedDevice.id,
                        CH_SERVICE_UUID,
                        RX_UUID,
                        base64Command
                    );

                    if(!result.value){
                        throw new Error('No response received');
                    }

                    const responseBytes = SNBCommandService.parseResponseFromBase64(result.value);

                    if(!SNBCommandService.validateResponse(responseBytes, expectedCommandId)){
                        throw new Error('Invalid response received');
                    }

                    // Toast.show({
                    //     type: 'success',
                    //     text1: 'Success',
                    //     text2: `${commandName} command sent successfully`,
                    //     position: 'bottom'
                    // });

                    afterSuccessCallback?.();

                    return result;

                } catch (error) {

                    failureCallback?.();
                    // Toast.show({
                    //     type: 'error',
                    //     text1: 'Error',
                    //     text2: `Failed to send ${commandName} command`,
                    //     position: 'bottom'
                    // });

                    throw error;
                }
            },

            getDataFrequency : async () => {
                const command = SNBCommandService.buildGetDataFrequencyCommand();
                return await get().sendCommand(command, 'Get Data Frequency', 0x01);
            },

            setDataFrequency : async (intervalSeconds: number) => {
                const command = SNBCommandService.buildSetDataFrequencyCommand(intervalSeconds);
                return await get().sendCommand(command, 'Set Data Frequency', 0x01);
            },

            cartrigeId1: '0',
            loadingCartridgeId1Details: false,
            cartrigeId1Details: null,
            cartrigeId2: '0',
            loadingCartridgeId2Details: false,
            cartrigeId2Details: null,

            fetchCartridgeDetails: async (cartridgeId: string) => {
                if(!cartridgeId || cartridgeId === '0' || cartridgeId === '00000000') {
                    // If both slots report the same empty ID, clear both instead of only the first match
                    if(get().cartrigeId1 === cartridgeId){
                        set({loadingCartridgeId1Details: false,cartrigeId1Details: null});
                    }
                    if(get().cartrigeId2 === cartridgeId){
                        set({loadingCartridgeId2Details: false,cartrigeId2Details: null});
                    }
                    return;
                };

                const response = await cartridgeService.getCartridgeDetails(cartridgeId);
                if(response.success){
                    if(get().cartrigeId1 === cartridgeId){
                        set({loadingCartridgeId1Details: false,cartrigeId1Details: response.data});
                    }
                    if(get().cartrigeId2 === cartridgeId){
                        set({loadingCartridgeId2Details: false,cartrigeId2Details: response.data});
                    }
                }
                else {
                    if(get().cartrigeId1 === cartridgeId){
                        set({loadingCartridgeId1Details: false,cartrigeId1Details: null});
                    }
                    
                    if(get().cartrigeId2 === cartridgeId){
                        set({loadingCartridgeId2Details: false,cartrigeId2Details: null});
                    }
                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: response.error.message,
                        position: 'bottom'
                    });
                }
            },

            dispensingTabletId: null,
            isDispensingTablet: false,
            isDispensingInProgress: false,
            isSyncingDispenseWithServer: false,
            isDispensingSuccess: false,
            isDispensingError: false,
            dispensingProgress: 0,
            setDispensingTabletId: (cartridgeId: string|null) => {
                set({dispensingTabletId: cartridgeId});
            },
            resetDispensingState: () => {
                set({dispensingTabletId: null,isDispensingTablet:false,isDispensingInProgress:false,isSyncingDispenseWithServer: false,isDispensingSuccess: false,isDispensingError: false,dispensingProgress: 0});
            },
            dispenseNutrition : async () => {
                const { isConnected, dispensingTabletId,isDispensingTablet,isDispensingInProgress } = get();

                if(!isConnected) {
                    Toast.show({
                        type: 'error',
                        text1: 'Connect Device',
                        text2: "Device not connected",
                        position: 'bottom'
                    });
                    return null;
                }
            
                if(!dispensingTabletId) {
                    Toast.show({
                    type: 'error',
                    text1: 'Invalid Cartridge',
                    text2: "Please select a valid cartridge",
                    position: 'bottom'
                    });
                    return null;
                };

                if(isDispensingTablet || isDispensingInProgress) {
                    Toast.show({
                    type: 'error',
                    text1: 'Already Dispensing',
                    text2: "Please wait for the current dispensing to complete",
                    position: 'bottom'
                    });
                    return null;
                }

                set({isDispensingTablet: true,isDispensingSuccess: false,isDispensingError: false,dispensingProgress: 0});
                
                const command = SNBCommandService.buildDispenseNutritionCommand(1);
                // (dispensingTabletId==get().cartrigeId1?1:2); // 1 = cartridge 1, 2 = cartridge 2

                const result = await get().sendCommand(command, 'Dispense Nutrition', 0x02,()=>{
                    set({isDispensingTablet: false,isDispensingInProgress: false,isDispensingSuccess: false,isDispensingError: true,dispensingProgress:0});
                },() => {
                    set({isDispensingInProgress: true});
                });
                return result;
            },
            syncDispenseWithServer: async (cartridgeId: string) => {
                // Remove the early return check here since we're handling it in the RX handler
                
                const response = await dispenseService.dispenseTablet(cartridgeId);

                set({
                    isSyncingDispenseWithServer: false,
                    isDispensingError: false,
                    isDispensingSuccess: true,
                    dispensingProgress: 100,
                });
                if(response.success){
                    if(cartridgeId === get().cartrigeId1){
                        set({cartrigeId1Details: {
                            ...get().cartrigeId1Details,
                            currentCount: response.data.currentCount
                        }});
                    }
                    else if(cartridgeId === get().cartrigeId2){
                        set({cartrigeId2Details: {
                            ...get().cartrigeId2Details,
                            currentCount: response.data.currentCount
                        }});
                    }

                    useDispenseStore.getState().getTodayDoseStatus();
                }
                else {
                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: response.error.message,
                        position: 'bottom'
                    });
                }
            },

            setDispensingProgress: (action: number | ((prev: number) => number)) => {
                set((state) => {
                    const next =
                    typeof action === "function"
                        ? (action as (prev: number) => number)(state.dispensingProgress)
                        : action;

                    return { dispensingProgress: next };
                });
            },

            getTimeStamp : async () => {
                const command = SNBCommandService.buildTimeSyncCommand();
                return await get().sendCommand(command, 'Get Time Stamp', 0x03);
            },

            setTimeStamp : async () => {
                const command = SNBCommandService.buildTimeSyncCommand();
                return await get().sendCommand(command, 'Set Time Stamp', 0x03);
            },

            getLiveSensorData : async () => {
                const command = SNBCommandService.buildGetLiveSensorDataCommand();
                return await get().sendCommand(command, 'Get Live Sensor Data', 0x04);
            },

            showScheduleTabletModalId: null,
            setScheduleTabletModal: (showScheduleTabletModal: string | null) => {
                set({showScheduleTabletModalId: showScheduleTabletModal});
            },
        }),
        {
            name: 'bluetooth-store',
            version: 1,
            storage: createJSONStorage(() => AsyncStorage),
            partialize: (state) => ({
                deviceId: state.deviceId,
                deviceName: state.deviceName,
                localDeviceName: state.localDeviceName,
                batteryPercentage: state.batteryPercentage,
                // dispensingTabletId: state.dispensingTabletId,
                // dispensingProgress: state.dispensingProgress,
                // isDispensingTablet: state.isDispensingTablet,
                // isDispensingInProgress: state.isDispensingInProgress,
            }),

        }
    )
)

export default useBluetoothStore;
